/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { useState, useCallback, useEffect } from 'react';
import { LoadedSettings, SettingScope } from '../../config/settings.js';
import {
  AuthType,
  Config,
  clearCachedCredentialFile,
  getErrorMessage,
} from '@google/gemini-cli-core';

export const useAuthCommand = (
  settings: LoadedSettings,
  setAuthError: (error: string | null) => void,
  config: Config,
) => {
  const [isAuthDialogOpen, setIsAuthDialogOpen] = useState(
    settings.merged.selectedAuthType === undefined,
  );

  const openAuthDialog = useCallback(() => {
    setIsAuthDialogOpen(true);
  }, []);

  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [shouldRefreshAuth, setShouldRefreshAuth] = useState(true); // Allow initial auth
  const [authErrorOccurred, setAuthErrorOccurred] = useState(false); // Track if auth error occurred

  useEffect(() => {
    const authFlow = async () => {
      const authType = settings.merged.selectedAuthType;
      if (isAuthDialogOpen || !authType || !shouldRefreshAuth) {
        return;
      }

      try {
        setIsAuthenticating(true);
        await config.refreshAuth(authType);
        console.log(`Authenticated via "${authType}".`);
        setShouldRefreshAuth(false); // Only prevent re-auth on success
        setAuthErrorOccurred(false); // Clear error flag on success
      } catch (e) {
        setAuthError(`Failed to login. Message: ${getErrorMessage(e)}`);
        setAuthErrorOccurred(true); // Mark that auth error occurred
        openAuthDialog();
        // Keep shouldRefreshAuth true for retry
      } finally {
        setIsAuthenticating(false);
      }
    };

    void authFlow();
  }, [isAuthDialogOpen, settings, config, setAuthError, openAuthDialog, shouldRefreshAuth]);

  const handleAuthSelect = useCallback(
    async (authType: AuthType | undefined, scope: SettingScope) => {
      if (authType) {
        await clearCachedCredentialFile();
        settings.setValue(scope, 'selectedAuthType', authType);
        setShouldRefreshAuth(true); // Only trigger refresh when auth type is actually changed
      } else {
        // User cancelled (pressed ESC)
        // Only disable re-auth if there was no previous auth error
        if (!authErrorOccurred) {
          setShouldRefreshAuth(false);
        }
        // If there was an auth error, keep shouldRefreshAuth true for retry
      }
      setIsAuthDialogOpen(false);
      setAuthError(null);
    },
    [settings, setAuthError, authErrorOccurred],
  );

  const cancelAuthentication = useCallback(() => {
    setIsAuthenticating(false);
  }, []);

  return {
    isAuthDialogOpen,
    openAuthDialog,
    handleAuthSelect,
    isAuthenticating,
    cancelAuthentication,
  };
};
