/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { useState, useCallback, useEffect } from 'react';
import { LoadedSettings, SettingScope } from '../../config/settings.js';
import {
  AuthType,
  Config,
  clearCachedCredentialFile,
  getErrorMessage,
} from '@google/gemini-cli-core';

export const useAuthCommand = (
  settings: LoadedSettings,
  setAuthError: (error: string | null) => void,
  config: Config,
) => {
  const [isAuthDialogOpen, setIsAuthDialogOpen] = useState(
    settings.merged.selectedAuthType === undefined,
  );

  const [forceReauth, setForceReauth] = useState(false);

  const openAuthDialog = useCallback(() => {
    setIsAuthDialogOpen(true);
    setForceReauth(true); // Mark that we need to force re-authentication
  }, []);

  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [lastAuthType, setLastAuthType] = useState<AuthType | undefined>(undefined);

  useEffect(() => {
    const authFlow = async () => {
      const authType = settings.merged.selectedAuthType;
      if (isAuthDialogOpen || !authType) {
        return;
      }

      // Perform authentication when:
      // 1. First time (lastAuthType is undefined)
      // 2. Auth type changed (different from lastAuthType)
      // 3. Force reauth was requested (e.g., due to auth error)
      const needsAuth = lastAuthType !== authType || forceReauth;

      if (needsAuth) {
        try {
          setIsAuthenticating(true);
          await config.refreshAuth(authType);
          console.log(`Authenticated via "${authType}".`);
          setLastAuthType(authType);
          setForceReauth(false); // Clear the force reauth flag
        } catch (e) {
          setAuthError(`Failed to login. Message: ${getErrorMessage(e)}`);
          openAuthDialog();
        } finally {
          setIsAuthenticating(false);
        }
      }
    };

    void authFlow();
  }, [isAuthDialogOpen, settings, config, setAuthError, openAuthDialog, lastAuthType, forceReauth]);

  const handleAuthSelect = useCallback(
    async (authType: AuthType | undefined, scope: SettingScope) => {
      if (authType) {
        await clearCachedCredentialFile();
        settings.setValue(scope, 'selectedAuthType', authType);
        // Don't update lastAuthType here - let the useEffect handle the auth flow
      }
      setIsAuthDialogOpen(false);
      setAuthError(null);
    },
    [settings, setAuthError],
  );

  const cancelAuthentication = useCallback(() => {
    setIsAuthenticating(false);
  }, []);

  return {
    isAuthDialogOpen,
    openAuthDialog,
    handleAuthSelect,
    isAuthenticating,
    cancelAuthentication,
  };
};
